#!/usr/bin/env node

/**
 * 代理连接测试脚本
 * 测试代理服务器是否正常工作
 */

import fetch from 'node-fetch';
import { HttpsProxyAgent } from 'https-proxy-agent';

/**
 * 测试代理连接
 */
async function testProxyConnection() {
  console.log('=== 测试代理连接 ===\n');
  
  const proxyUrl = 'http://127.0.0.1:7897';
  console.log(`代理地址: ${proxyUrl}`);
  
  try {
    // 测试代理是否可达
    console.log('1. 测试代理服务器是否可达...');
    
    const agent = new HttpsProxyAgent(proxyUrl);
    
    // 尝试通过代理访问一个简单的HTTPS网站
    const testUrl = 'https://httpbin.org/ip';
    console.log(`2. 通过代理访问测试网站: ${testUrl}`);
    
    const response = await fetch(testUrl, {
      method: 'GET',
      agent: agent,
      timeout: 10000
    });
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ 代理连接成功');
      console.log('代理IP信息:', data);
      return true;
    } else {
      console.log('❌ 代理响应异常:', response.status);
      return false;
    }
    
  } catch (error) {
    console.log('❌ 代理连接失败:', error.message);
    console.log('\n可能的原因:');
    console.log('1. 代理服务器未启动或不在 127.0.0.1:7897 上运行');
    console.log('2. 代理服务器配置错误');
    console.log('3. 网络连接问题');
    return false;
  }
}

/**
 * 测试直接连接
 */
async function testDirectConnection() {
  console.log('\n=== 测试直接连接 ===\n');
  
  try {
    const testUrl = 'https://httpbin.org/ip';
    console.log(`直接访问测试网站: ${testUrl}`);
    
    const response = await fetch(testUrl, {
      method: 'GET',
      timeout: 10000
    });
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ 直接连接成功');
      console.log('本地IP信息:', data);
      return true;
    } else {
      console.log('❌ 直接连接响应异常:', response.status);
      return false;
    }
    
  } catch (error) {
    console.log('❌ 直接连接失败:', error.message);
    return false;
  }
}

/**
 * 测试 Anthropic API 连接
 */
async function testAnthropicConnection() {
  console.log('\n=== 测试 Anthropic API 连接 ===\n');
  
  const apiKey = '************************************************************************************************************';
  
  // 测试通过代理连接
  console.log('1. 尝试通过代理连接 Anthropic API...');
  try {
    const proxyUrl = 'http://127.0.0.1:7897';
    const agent = new HttpsProxyAgent(proxyUrl);
    
    const response = await fetch('https://api.anthropic.com/v1/models', {
      method: 'GET',
      headers: {
        'x-api-key': apiKey,
        'anthropic-version': '2023-06-01',
        'Content-Type': 'application/json'
      },
      agent: agent,
      timeout: 15000
    });
    
    console.log('代理请求响应状态:', response.status);
    
    if (response.ok) {
      console.log('✅ 通过代理连接 Anthropic API 成功');
      return true;
    } else {
      const errorText = await response.text();
      console.log('❌ 代理连接 Anthropic API 失败:', errorText);
    }
  } catch (proxyError) {
    console.log('❌ 代理连接 Anthropic API 错误:', proxyError.message);
  }
  
  // 测试直接连接
  console.log('\n2. 尝试直接连接 Anthropic API...');
  try {
    const response = await fetch('https://api.anthropic.com/v1/models', {
      method: 'GET',
      headers: {
        'x-api-key': apiKey,
        'anthropic-version': '2023-06-01',
        'Content-Type': 'application/json'
      },
      timeout: 15000
    });
    
    console.log('直接请求响应状态:', response.status);
    
    if (response.ok) {
      console.log('✅ 直接连接 Anthropic API 成功');
      return true;
    } else {
      const errorText = await response.text();
      console.log('❌ 直接连接 Anthropic API 失败:', errorText);
    }
  } catch (directError) {
    console.log('❌ 直接连接 Anthropic API 错误:', directError.message);
  }
  
  return false;
}

/**
 * 运行所有测试
 */
async function runAllTests() {
  console.log('开始代理连接测试...\n');
  
  // 测试代理连接
  const proxyOk = await testProxyConnection();
  
  // 测试直接连接
  const directOk = await testDirectConnection();
  
  // 测试 Anthropic API 连接
  const anthropicOk = await testAnthropicConnection();
  
  console.log('\n=== 测试结果总结 ===');
  console.log(`代理连接: ${proxyOk ? '✅ 成功' : '❌ 失败'}`);
  console.log(`直接连接: ${directOk ? '✅ 成功' : '❌ 失败'}`);
  console.log(`Anthropic API: ${anthropicOk ? '✅ 成功' : '❌ 失败'}`);
  
  if (!proxyOk && !directOk) {
    console.log('\n⚠️  网络连接完全失败，请检查网络设置');
  } else if (!proxyOk && directOk) {
    console.log('\n💡 建议：代理连接失败但直接连接成功，可以尝试不使用代理');
  } else if (proxyOk && !anthropicOk) {
    console.log('\n💡 建议：代理工作正常但 Anthropic API 连接失败，请检查 API 密钥或 API 服务状态');
  }
  
  console.log('\n测试完成！');
}

// 运行测试
runAllTests().catch(console.error);

export {
  testProxyConnection,
  testDirectConnection,
  testAnthropicConnection,
  runAllTests
};
