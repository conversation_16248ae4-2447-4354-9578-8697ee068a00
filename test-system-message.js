#!/usr/bin/env node

/**
 * 专门测试 system 消息的脚本
 */

import fetch from 'node-fetch';

const API_KEY = '************************************************************************************************************';

/**
 * 测试 system 消息 - 方式1：在 messages 数组中
 */
async function testSystemInMessages() {
  console.log('=== 测试 System 消息（在 messages 数组中）===\n');

  const requestBody = {
    model: 'claude-3-haiku-20240307',
    max_tokens: 500,
    messages: [
      {
        role: 'system',
        content: 'You are a helpful assistant that always responds cheerfully.'
      },
      {
        role: 'user',
        content: 'Hello, how are you today?'
      }
    ],
    temperature: 0.7
  };

  console.log('发送请求（system 在 messages 中）:', JSON.stringify(requestBody, null, 2));

  try {
    const response = await fetch('http://127.0.0.1:3001/api/anthropic/messages', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': API_KEY,
        'anthropic-version': '2023-06-01'
      },
      body: JSON.stringify(requestBody)
    });

    console.log('响应状态:', response.status);

    if (response.ok) {
      const data = await response.json();
      console.log('✅ 请求成功');
      console.log('响应数据:', JSON.stringify(data, null, 2));
    } else {
      const errorText = await response.text();
      console.log('❌ 请求失败:', errorText);
    }

  } catch (error) {
    console.log('❌ 请求错误:', error.message);
  }
}

/**
 * 测试 system 消息 - 方式2：作为顶级参数
 */
async function testSystemAsTopLevel() {
  console.log('\n=== 测试 System 消息（作为顶级参数）===\n');

  const requestBody = {
    model: 'claude-3-haiku-20240307',
    max_tokens: 500,
    system: 'You are a helpful assistant that always responds cheerfully.',
    messages: [
      {
        role: 'user',
        content: 'Hello, how are you today?'
      }
    ],
    temperature: 0.7
  };

  console.log('发送请求（system 作为顶级参数）:', JSON.stringify(requestBody, null, 2));

  try {
    const response = await fetch('http://127.0.0.1:3001/api/anthropic/messages', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': API_KEY,
        'anthropic-version': '2023-06-01'
      },
      body: JSON.stringify(requestBody)
    });

    console.log('响应状态:', response.status);

    if (response.ok) {
      const data = await response.json();
      console.log('✅ 请求成功');
      console.log('响应数据:', JSON.stringify(data, null, 2));
    } else {
      const errorText = await response.text();
      console.log('❌ 请求失败:', errorText);
    }

  } catch (error) {
    console.log('❌ 请求错误:', error.message);
  }
}

/**
 * 测试简单消息（无 system）
 */
async function testSimpleMessage() {
  console.log('\n=== 测试简单消息（无 system）===\n');

  const requestBody = {
    model: 'claude-3-haiku-20240307',
    max_tokens: 500,
    messages: [
      {
        role: 'user',
        content: 'Hello, how are you today?'
      }
    ],
    temperature: 0.7
  };

  console.log('发送简单请求:', JSON.stringify(requestBody, null, 2));

  try {
    const response = await fetch('http://127.0.0.1:3001/api/anthropic/messages', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': API_KEY,
        'anthropic-version': '2023-06-01'
      },
      body: JSON.stringify(requestBody)
    });

    console.log('响应状态:', response.status);

    if (response.ok) {
      const data = await response.json();
      console.log('✅ 请求成功');
      console.log('响应数据:', JSON.stringify(data, null, 2));
    } else {
      const errorText = await response.text();
      console.log('❌ 请求失败:', errorText);
    }

  } catch (error) {
    console.log('❌ 请求错误:', error.message);
  }
}

/**
 * 运行所有测试
 */
async function runAllTests() {
  console.log('开始 System 消息测试...\n');

  // 测试简单消息
  await testSimpleMessage();
  await new Promise(resolve => setTimeout(resolve, 2000));

  // 测试 system 作为顶级参数
  await testSystemAsTopLevel();
  await new Promise(resolve => setTimeout(resolve, 2000));

  // 测试 system 在 messages 中
  await testSystemInMessages();

  console.log('\n所有测试完成！');
}

// 运行测试
runAllTests().catch(console.error);

export {
  testSystemInMessages,
  testSystemAsTopLevel,
  testSimpleMessage,
  runAllTests
};
