#!/usr/bin/env node

/**
 * 测试工具调用修复
 */

import fetch from 'node-fetch';

const API_KEY = '************************************************************************************************************';

/**
 * 测试完整的工具调用流程
 */
async function testToolCallFix() {
  console.log('🔧 测试工具调用修复...\n');

  // 发送包含工具的消息给 Claude
  const requestBody = {
    model: 'claude-3-haiku-20240307',
    max_tokens: 1000,
    messages: [
      {
        role: 'user',
        content: '请帮我创建一个文件 /Users/<USER>/Desktop/fix_test.txt，内容是 "Tool call fix test - this should work now!"'
      }
    ],
    tools: [
      {
        name: 'write_file',
        description: 'Write content to a file',
        input_schema: {
          type: 'object',
          properties: {
            path: {
              type: 'string',
              description: 'The file path to write to'
            },
            content: {
              type: 'string',
              description: 'The content to write to the file'
            }
          },
          required: ['path', 'content']
        }
      }
    ],
    temperature: 0.7
  };

  console.log('1️⃣ 发送消息给 Claude（包含工具定义）...');
  
  try {
    const response = await fetch('http://127.0.0.1:3001/api/anthropic/messages', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': API_KEY,
        'anthropic-version': '2023-06-01'
      },
      body: JSON.stringify(requestBody)
    });

    console.log('响应状态:', response.status);

    if (response.ok) {
      const data = await response.json();
      console.log('✅ Claude 响应成功');
      
      // 检查是否有工具调用
      if (data.content) {
        let hasToolUse = false;
        for (const content of data.content) {
          if (content.type === 'tool_use') {
            hasToolUse = true;
            console.log('\n2️⃣ 发现工具调用:');
            console.log('工具名称:', content.name);
            console.log('工具ID:', content.id);
            console.log('工具参数:', JSON.stringify(content.input, null, 2));
            
            // 测试直接工具调用
            console.log('\n3️⃣ 执行工具调用...');
            const toolResult = await testDirectToolCall(content.name, content.input);
            
            if (toolResult) {
              console.log('\n4️⃣ 分析工具调用结果:');
              console.log('原始结果:', JSON.stringify(toolResult, null, 2));
              
              // 模拟前端的结果提取逻辑
              let extractedResult = undefined;
              if (toolResult.result) {
                if (toolResult.result.content && Array.isArray(toolResult.result.content)) {
                  extractedResult = toolResult.result.content
                    .filter(item => item.type === 'text')
                    .map(item => item.text)
                    .join('\n');
                  console.log('✅ 提取的结果文本:', extractedResult);
                } else {
                  extractedResult = toolResult.result;
                  console.log('⚠️  使用原始结果:', extractedResult);
                }
              }
              
              // 模拟前端工具调用卡片显示
              const toolCallCard = {
                type: 'tool_calls',
                tool_calls: [{
                  name: content.name,
                  arguments: content.input,
                  result: extractedResult,
                  success: true
                }]
              };
              
              console.log('\n5️⃣ 前端工具调用卡片JSON:');
              console.log(JSON.stringify(toolCallCard, null, 2));
              
              // 验证结果不是 null
              if (extractedResult !== null && extractedResult !== undefined) {
                console.log('\n🎉 修复成功！工具调用结果不再是 null');
              } else {
                console.log('\n❌ 修复失败，结果仍然是 null');
              }
            }
          }
        }
        
        if (!hasToolUse) {
          console.log('⚠️  Claude 没有调用工具，可能直接回答了问题');
          console.log('Claude 的回复:', data.content[0].text);
        }
      }
    } else {
      const errorText = await response.text();
      console.log('❌ Claude 响应失败:', errorText);
    }

  } catch (error) {
    console.log('❌ 请求错误:', error.message);
  }
}

/**
 * 直接测试工具调用
 */
async function testDirectToolCall(toolName, args) {
  try {
    console.log(`直接调用工具 ${toolName}...`);
    
    const response = await fetch('http://127.0.0.1:3001/mcp/tools/call', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        name: toolName,
        arguments: args
      })
    });
    
    if (response.ok) {
      const result = await response.json();
      console.log('✅ 工具调用成功');
      return result;
    } else {
      const errorText = await response.text();
      console.log('❌ 工具调用失败:', errorText);
      return null;
    }
  } catch (error) {
    console.log('❌ 工具调用错误:', error.message);
    return null;
  }
}

/**
 * 测试流式处理（模拟前端的实际使用方式）
 */
async function testStreamProcessing() {
  console.log('\n\n🌊 测试流式处理...\n');
  
  try {
    const response = await fetch('http://127.0.0.1:3001/api/chat/stream', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': API_KEY
      },
      body: JSON.stringify({
        message: '请帮我创建一个文件 /Users/<USER>/Desktop/stream_test.txt，内容是 "Stream processing test"',
        model: 'claude-3-haiku-20240307'
      })
    });
    
    if (response.ok && response.body) {
      console.log('✅ 开始接收流式响应...');
      
      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let buffer = '';
      
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;
        
        buffer += decoder.decode(value, { stream: true });
        
        // 查找工具调用JSON
        const toolCallMatches = buffer.match(/\{"type":"tool_calls"[^}]+\}/g);
        if (toolCallMatches) {
          toolCallMatches.forEach(match => {
            try {
              const toolCallData = JSON.parse(match);
              console.log('\n📋 发现工具调用数据:');
              console.log(JSON.stringify(toolCallData, null, 2));
              
              if (toolCallData.tool_calls && toolCallData.tool_calls[0]) {
                const toolCall = toolCallData.tool_calls[0];
                console.log('工具调用结果:', toolCall.result);
                
                if (toolCall.result !== null && toolCall.result !== undefined) {
                  console.log('🎉 流式处理中的工具调用结果正常！');
                } else {
                  console.log('❌ 流式处理中的工具调用结果仍然是 null');
                }
              }
            } catch (e) {
              // 忽略解析错误
            }
          });
        }
      }
    } else {
      console.log('❌ 流式响应失败');
    }
  } catch (error) {
    console.log('❌ 流式处理错误:', error.message);
  }
}

// 运行测试
async function runAllTests() {
  await testToolCallFix();
  await testStreamProcessing();
  console.log('\n所有测试完成！');
}

runAllTests().catch(console.error);
