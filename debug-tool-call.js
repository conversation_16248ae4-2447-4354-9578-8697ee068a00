#!/usr/bin/env node

/**
 * 调试工具调用流程
 */

import fetch from 'node-fetch';

const API_KEY = '************************************************************************************************************';

/**
 * 测试完整的工具调用流程
 */
async function debugToolCallFlow() {
  console.log('🔍 调试工具调用流程...\n');

  // 第一步：发送包含工具的消息给 Claude
  const requestBody = {
    model: 'claude-3-haiku-20240307',
    max_tokens: 1000,
    messages: [
      {
        role: 'user',
        content: '请帮我创建一个文件 /Users/<USER>/Desktop/debug.txt，内容是 "Debug test file"'
      }
    ],
    tools: [
      {
        name: 'write_file',
        description: 'Write content to a file',
        input_schema: {
          type: 'object',
          properties: {
            path: {
              type: 'string',
              description: 'The file path to write to'
            },
            content: {
              type: 'string',
              description: 'The content to write to the file'
            }
          },
          required: ['path', 'content']
        }
      }
    ],
    temperature: 0.7
  };

  console.log('1️⃣ 发送消息给 Claude...');
  
  try {
    const response = await fetch('http://127.0.0.1:3001/api/anthropic/messages', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': API_KEY,
        'anthropic-version': '2023-06-01'
      },
      body: JSON.stringify(requestBody)
    });

    console.log('响应状态:', response.status);

    if (response.ok) {
      const data = await response.json();
      console.log('✅ Claude 响应成功');
      console.log('响应内容:');
      console.log(JSON.stringify(data, null, 2));
      
      // 检查是否有工具调用
      if (data.content) {
        let hasToolUse = false;
        for (const content of data.content) {
          if (content.type === 'tool_use') {
            hasToolUse = true;
            console.log('\n2️⃣ 发现工具调用:');
            console.log('工具名称:', content.name);
            console.log('工具ID:', content.id);
            console.log('工具参数:', JSON.stringify(content.input, null, 2));
            
            // 第二步：执行工具调用
            console.log('\n3️⃣ 执行工具调用...');
            await testDirectToolCall(content.name, content.input);
            
            // 第三步：模拟前端处理
            console.log('\n4️⃣ 模拟前端工具调用处理...');
            await simulateFrontendToolCall(content);
          }
        }
        
        if (!hasToolUse) {
          console.log('⚠️  Claude 没有调用工具，可能直接回答了问题');
        }
      }
    } else {
      const errorText = await response.text();
      console.log('❌ Claude 响应失败:', errorText);
    }

  } catch (error) {
    console.log('❌ 请求错误:', error.message);
  }
}

/**
 * 直接测试工具调用
 */
async function testDirectToolCall(toolName, args) {
  try {
    console.log(`直接调用工具 ${toolName}...`);
    
    const response = await fetch('http://127.0.0.1:3001/mcp/tools/call', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        name: toolName,
        arguments: args
      })
    });
    
    if (response.ok) {
      const result = await response.json();
      console.log('✅ 工具调用成功');
      console.log('工具调用结果:', JSON.stringify(result, null, 2));
      return result;
    } else {
      const errorText = await response.text();
      console.log('❌ 工具调用失败:', errorText);
      return null;
    }
  } catch (error) {
    console.log('❌ 工具调用错误:', error.message);
    return null;
  }
}

/**
 * 模拟前端工具调用处理
 */
async function simulateFrontendToolCall(toolUse) {
  console.log('模拟前端处理工具调用...');
  
  // 模拟前端调用工具
  const toolResult = await testDirectToolCall(toolUse.name, toolUse.input);
  
  if (toolResult) {
    // 模拟前端生成的工具调用结果JSON
    const frontendToolCallJson = {
      type: 'tool_calls',
      tool_calls: [{
        name: toolUse.name,
        arguments: toolUse.input,
        result: toolResult.result,
        success: true
      }]
    };
    
    console.log('前端生成的工具调用JSON:');
    console.log(JSON.stringify(frontendToolCallJson, null, 2));
    
    // 模拟发送工具调用结果回给 Claude
    console.log('\n5️⃣ 发送工具调用结果回给 Claude...');
    await sendToolResultToClaude(toolUse, toolResult);
  }
}

/**
 * 发送工具调用结果回给 Claude
 */
async function sendToolResultToClaude(toolUse, toolResult) {
  const requestBody = {
    model: 'claude-3-haiku-20240307',
    max_tokens: 1000,
    messages: [
      {
        role: 'user',
        content: '请帮我创建一个文件 /Users/<USER>/Desktop/debug.txt，内容是 "Debug test file"'
      },
      {
        role: 'assistant',
        content: [
          {
            type: 'tool_use',
            id: toolUse.id,
            name: toolUse.name,
            input: toolUse.input
          }
        ]
      },
      {
        role: 'user',
        content: [
          {
            type: 'tool_result',
            tool_use_id: toolUse.id,
            content: JSON.stringify(toolResult.result)
          }
        ]
      }
    ],
    temperature: 0.7
  };

  try {
    const response = await fetch('http://127.0.0.1:3001/api/anthropic/messages', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': API_KEY,
        'anthropic-version': '2023-06-01'
      },
      body: JSON.stringify(requestBody)
    });

    if (response.ok) {
      const data = await response.json();
      console.log('✅ Claude 处理工具结果成功');
      console.log('Claude 的最终回复:', data.content[0].text);
    } else {
      const errorText = await response.text();
      console.log('❌ Claude 处理工具结果失败:', errorText);
    }
  } catch (error) {
    console.log('❌ 发送工具结果错误:', error.message);
  }
}

// 运行调试
debugToolCallFlow().catch(console.error);
