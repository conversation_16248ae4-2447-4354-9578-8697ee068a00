#!/usr/bin/env node

/**
 * 测试工具执行修复
 */

import fetch from 'node-fetch';

const API_KEY = '************************************************************************************************************';

/**
 * 测试流式工具执行
 */
async function testStreamToolExecution() {
  console.log('🔧 测试流式工具执行修复...\n');

  const requestBody = {
    model: 'claude-3-haiku-20240307',
    max_tokens: 1000,
    messages: [
      {
        role: 'user',
        content: '请帮我创建一个文件 /Users/<USER>/Desktop/execution_test.txt，内容是 "Tool execution fix test - this should actually execute now!"'
      }
    ],
    tools: [
      {
        name: 'write_file',
        description: 'Write content to a file',
        input_schema: {
          type: 'object',
          properties: {
            path: {
              type: 'string',
              description: 'The file path to write to'
            },
            content: {
              type: 'string',
              description: 'The content to write to the file'
            }
          },
          required: ['path', 'content']
        }
      }
    ],
    stream: true,
    temperature: 0.7
  };

  console.log('发送流式请求...');
  
  try {
    const response = await fetch('http://127.0.0.1:3001/api/anthropic/messages', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': API_KEY,
        'anthropic-version': '2023-06-01'
      },
      body: JSON.stringify(requestBody)
    });

    console.log('响应状态:', response.status);

    if (response.ok) {
      console.log('✅ 开始接收流式响应...\n');
      
      const responseText = await response.text();
      console.log('完整响应:', responseText);
      
      // 检查响应中是否包含工具调用相关的内容
      if (responseText.includes('tool_use')) {
        console.log('✅ 发现工具调用定义');
      }
      
      if (responseText.includes('Successfully wrote')) {
        console.log('🎉 发现工具执行成功的消息！');
      }
      
    } else {
      const errorText = await response.text();
      console.log('❌ 流式响应失败:', errorText);
    }

  } catch (error) {
    console.log('❌ 请求错误:', error.message);
  }
}

/**
 * 测试非流式工具执行
 */
async function testNonStreamToolExecution() {
  console.log('\n\n🔧 测试非流式工具执行修复...\n');

  const requestBody = {
    model: 'claude-3-haiku-20240307',
    max_tokens: 1000,
    messages: [
      {
        role: 'user',
        content: '请帮我创建一个文件 /Users/<USER>/Desktop/non_stream_test.txt，内容是 "Non-stream tool execution test"'
      }
    ],
    tools: [
      {
        name: 'write_file',
        description: 'Write content to a file',
        input_schema: {
          type: 'object',
          properties: {
            path: {
              type: 'string',
              description: 'The file path to write to'
            },
            content: {
              type: 'string',
              description: 'The content to write to the file'
            }
          },
          required: ['path', 'content']
        }
      }
    ],
    temperature: 0.7
  };

  console.log('发送非流式请求...');
  
  try {
    const response = await fetch('http://127.0.0.1:3001/api/anthropic/messages', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': API_KEY,
        'anthropic-version': '2023-06-01'
      },
      body: JSON.stringify(requestBody)
    });

    console.log('响应状态:', response.status);

    if (response.ok) {
      const data = await response.json();
      console.log('✅ 非流式响应成功');
      console.log('响应内容:', JSON.stringify(data, null, 2));
      
      // 检查是否有工具调用
      if (data.content) {
        let hasToolUse = false;
        for (const content of data.content) {
          if (content.type === 'tool_use') {
            hasToolUse = true;
            console.log('✅ 发现工具调用:', content.name);
          }
        }
        
        if (!hasToolUse) {
          console.log('⚠️  没有发现工具调用');
        }
      }
      
    } else {
      const errorText = await response.text();
      console.log('❌ 非流式响应失败:', errorText);
    }

  } catch (error) {
    console.log('❌ 请求错误:', error.message);
  }
}

/**
 * 验证文件是否创建成功
 */
async function verifyFileCreation() {
  console.log('\n\n📁 验证文件创建...\n');
  
  const filesToCheck = [
    '/Users/<USER>/Desktop/execution_test.txt',
    '/Users/<USER>/Desktop/non_stream_test.txt'
  ];
  
  for (const filePath of filesToCheck) {
    try {
      const response = await fetch('http://127.0.0.1:3001/mcp/tools/call', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          name: 'read_file',
          arguments: { path: filePath }
        })
      });
      
      if (response.ok) {
        const result = await response.json();
        console.log(`✅ 文件 ${filePath} 存在`);
        if (result.result && result.result.content) {
          const content = result.result.content
            .filter(item => item.type === 'text')
            .map(item => item.text)
            .join('\n');
          console.log(`   内容: ${content.substring(0, 50)}...`);
        }
      } else {
        console.log(`❌ 文件 ${filePath} 不存在或无法读取`);
      }
    } catch (error) {
      console.log(`❌ 检查文件 ${filePath} 时出错:`, error.message);
    }
  }
}

/**
 * 运行所有测试
 */
async function runAllTests() {
  console.log('🚀 开始测试工具执行修复...\n');
  
  await testStreamToolExecution();
  await testNonStreamToolExecution();
  await verifyFileCreation();
  
  console.log('\n🎯 测试总结:');
  console.log('1. 如果看到"工具执行成功"的消息，说明修复有效');
  console.log('2. 如果文件创建成功，说明工具实际被执行了');
  console.log('3. 前端应该不再显示"正在等待结果..."');
  
  console.log('\n所有测试完成！');
}

runAllTests().catch(console.error);
