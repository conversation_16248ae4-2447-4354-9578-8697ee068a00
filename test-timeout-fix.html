<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工具调用超时修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .tool-call-container {
            margin: 8px 0;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            overflow: hidden;
            background-color: rgba(255, 255, 255, 0.85);
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        }
        .tool-call-header {
            display: flex;
            align-items: center;
            padding: 12px 16px;
            cursor: pointer;
            background-color: rgba(248, 249, 250, 0.8);
        }
        .tool-call-status {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            margin-right: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .tool-call-status.pending {
            background-color: #1e88e5;
            animation: pulse 1.5s infinite;
        }
        .tool-call-status.success {
            background-color: #4caf50;
        }
        .tool-call-status.error {
            background-color: #f44336;
        }
        .tool-call-info {
            flex: 1;
        }
        .tool-name {
            font-weight: 600;
            color: #333;
            display: block;
            margin-bottom: 2px;
        }
        .tool-status {
            font-size: 0.85rem;
            color: #666;
        }
        .tool-status.pending {
            color: #1e88e5;
        }
        .tool-status.success {
            color: #4caf50;
        }
        .tool-status.error {
            color: #f44336;
        }
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            background-color: #007bff;
            color: white;
        }
        button:hover {
            background-color: #0056b3;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>工具调用超时修复测试</h1>
    
    <div class="test-section">
        <h2>测试1: 模拟正在调用中的工具</h2>
        <p>这个工具调用会在30秒后自动超时</p>
        <div id="test1-container"></div>
        <button onclick="startTest1()">开始测试1</button>
        <div id="test1-log" class="log"></div>
    </div>
    
    <div class="test-section">
        <h2>测试2: 模拟成功的工具调用</h2>
        <p>这个工具调用会在3秒后模拟成功</p>
        <div id="test2-container"></div>
        <button onclick="startTest2()">开始测试2</button>
        <div id="test2-log" class="log"></div>
    </div>
    
    <div class="test-section">
        <h2>测试3: 模拟失败的工具调用</h2>
        <p>这个工具调用会在2秒后模拟失败</p>
        <div id="test3-container"></div>
        <button onclick="startTest3()">开始测试3</button>
        <div id="test3-log" class="log"></div>
    </div>

    <script>
        // 模拟工具调用组件
        class ToolCallComponent {
            constructor(container, toolName, params) {
                this.container = container;
                this.toolName = toolName;
                this.params = params;
                this.result = undefined;
                this.error = undefined;
                this.success = true;
                this.isTimedOut = false;
                this.timeoutId = null;
                this.TIMEOUT_DURATION = 30000; // 30秒超时
                
                this.render();
                this.startTimeout();
            }
            
            render() {
                const hasResult = this.result !== undefined || this.error !== undefined || this.isTimedOut;
                const statusClass = this.isTimedOut ? 'error' : 
                                  hasResult ? (this.success ? 'success' : 'error') : 'pending';
                
                const statusText = this.isTimedOut ? '调用超时' :
                                 hasResult ? (this.success ? '调用成功' : '调用失败') : '正在调用中...';
                
                this.container.innerHTML = `
                    <div class="tool-call-container">
                        <div class="tool-call-header">
                            <div class="tool-call-status ${statusClass}">
                                ${hasResult && this.success && !this.isTimedOut ? '✓' : 
                                  hasResult && (!this.success || this.isTimedOut) ? '✗' : '●'}
                            </div>
                            <div class="tool-call-info">
                                <span class="tool-name">${this.toolName}</span>
                                <span class="tool-status ${statusClass}">${statusText}</span>
                            </div>
                        </div>
                    </div>
                `;
            }
            
            startTimeout() {
                if (this.result === undefined && this.error === undefined) {
                    this.timeoutId = setTimeout(() => {
                        if (this.result === undefined && this.error === undefined) {
                            this.isTimedOut = true;
                            this.log('工具调用超时');
                            this.render();
                        }
                    }, this.TIMEOUT_DURATION);
                }
            }
            
            setResult(result) {
                if (this.timeoutId) {
                    clearTimeout(this.timeoutId);
                }
                this.result = result;
                this.success = true;
                this.log(`工具调用成功: ${JSON.stringify(result)}`);
                this.render();
            }
            
            setError(error) {
                if (this.timeoutId) {
                    clearTimeout(this.timeoutId);
                }
                this.error = error;
                this.success = false;
                this.log(`工具调用失败: ${error}`);
                this.render();
            }
            
            log(message) {
                const logElement = document.getElementById(this.container.id.replace('-container', '-log'));
                if (logElement) {
                    const timestamp = new Date().toLocaleTimeString();
                    logElement.innerHTML += `[${timestamp}] ${message}\n`;
                    logElement.scrollTop = logElement.scrollHeight;
                }
            }
        }
        
        let test1Component, test2Component, test3Component;
        
        function startTest1() {
            const container = document.getElementById('test1-container');
            container.id = 'test1-container'; // 确保ID正确
            document.getElementById('test1-log').innerHTML = '';
            
            test1Component = new ToolCallComponent(container, 'write_file', {
                path: '/Users/<USER>/Desktop/test.txt',
                content: 'Test content'
            });
            
            test1Component.log('开始测试1: 模拟长时间运行的工具调用');
            test1Component.log('等待30秒后将自动超时...');
        }
        
        function startTest2() {
            const container = document.getElementById('test2-container');
            container.id = 'test2-container';
            document.getElementById('test2-log').innerHTML = '';
            
            test2Component = new ToolCallComponent(container, 'write_file', {
                path: '/Users/<USER>/Desktop/success.txt',
                content: 'Success content'
            });
            
            test2Component.log('开始测试2: 模拟成功的工具调用');
            test2Component.log('3秒后将模拟成功...');
            
            setTimeout(() => {
                test2Component.setResult({
                    content: [{
                        type: 'text',
                        text: 'Successfully wrote to /Users/<USER>/Desktop/success.txt'
                    }]
                });
            }, 3000);
        }
        
        function startTest3() {
            const container = document.getElementById('test3-container');
            container.id = 'test3-container';
            document.getElementById('test3-log').innerHTML = '';
            
            test3Component = new ToolCallComponent(container, 'write_file', {
                path: '/invalid/path/error.txt',
                content: 'Error content'
            });
            
            test3Component.log('开始测试3: 模拟失败的工具调用');
            test3Component.log('2秒后将模拟失败...');
            
            setTimeout(() => {
                test3Component.setError('Permission denied: cannot write to /invalid/path/error.txt');
            }, 2000);
        }
    </script>
</body>
</html>
