#!/usr/bin/env node

/**
 * 测试 Claude tool_use 格式处理修复
 */

import fetch from 'node-fetch';

const API_KEY = '************************************************************************************************************';

/**
 * 模拟前端的工具调用处理逻辑
 */
function simulateFrontendToolCallProcessing(claudeResponse) {
  console.log('🔧 模拟前端处理 Claude 响应...\n');
  
  try {
    // 检查是否为Claude原生的tool_use格式
    let toolCalls = [];
    if (claudeResponse.content && Array.isArray(claudeResponse.content)) {
      for (const content of claudeResponse.content) {
        if (content.type === 'tool_use') {
          toolCalls.push({
            name: content.name,
            arguments: content.input || {}
          });
        }
      }
    }
    
    // 或者检查是否为我们的工具调用格式
    if (claudeResponse.type === 'tool_calls' && claudeResponse.tool_calls?.length > 0) {
      toolCalls = claudeResponse.tool_calls;
    }

    if (toolCalls.length > 0) {
      console.log('✅ 发现工具调用，开始处理...');
      
      for (const call of toolCalls) {
        console.log(`🔧 处理工具调用: ${call.name}`);
        console.log('参数:', JSON.stringify(call.arguments, null, 2));
        
        // 这里应该调用实际的工具
        console.log('📞 调用 MCP 工具...');
        
        // 模拟工具调用结果
        const mockResult = {
          success: true,
          result: `Successfully executed ${call.name} with arguments: ${JSON.stringify(call.arguments)}`
        };
        
        console.log('✅ 工具调用成功:', mockResult.result);
        
        // 生成前端工具调用JSON
        const toolCallJson = {
          type: 'tool_calls',
          tool_calls: [{
            name: call.name,
            arguments: call.arguments,
            result: mockResult.result,
            success: true
          }]
        };
        
        console.log('📋 前端工具调用JSON:');
        console.log(JSON.stringify(toolCallJson, null, 2));
      }
      
      return true;
    } else {
      console.log('⚠️  没有发现工具调用');
      return false;
    }
    
  } catch (error) {
    console.error('❌ 处理工具调用时出错:', error);
    return false;
  }
}

/**
 * 测试 Claude 工具调用响应处理
 */
async function testClaudeToolUseProcessing() {
  console.log('🧪 测试 Claude tool_use 格式处理修复...\n');

  const requestBody = {
    model: 'claude-3-haiku-20240307',
    max_tokens: 1000,
    messages: [
      {
        role: 'user',
        content: '请帮我创建一个文件 /Users/<USER>/Desktop/claude_fix_test.txt，内容是 "Claude tool_use format fix test"'
      }
    ],
    tools: [
      {
        name: 'write_file',
        description: 'Write content to a file',
        input_schema: {
          type: 'object',
          properties: {
            path: {
              type: 'string',
              description: 'The file path to write to'
            },
            content: {
              type: 'string',
              description: 'The content to write to the file'
            }
          },
          required: ['path', 'content']
        }
      }
    ],
    temperature: 0.7
  };

  console.log('1️⃣ 发送请求给 Claude...');
  
  try {
    const response = await fetch('http://127.0.0.1:3001/api/anthropic/messages', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': API_KEY,
        'anthropic-version': '2023-06-01'
      },
      body: JSON.stringify(requestBody)
    });

    console.log('响应状态:', response.status);

    if (response.ok) {
      const data = await response.json();
      console.log('✅ Claude 响应成功\n');
      
      console.log('2️⃣ Claude 原始响应:');
      console.log(JSON.stringify(data, null, 2));
      
      console.log('\n3️⃣ 模拟前端处理:');
      const toolCallProcessed = simulateFrontendToolCallProcessing(data);
      
      if (toolCallProcessed) {
        console.log('\n🎉 修复成功！前端现在可以正确处理 Claude 的 tool_use 格式');
      } else {
        console.log('\n❌ 修复失败，前端仍然无法处理 tool_use 格式');
      }
      
    } else {
      const errorText = await response.text();
      console.log('❌ Claude 响应失败:', errorText);
    }

  } catch (error) {
    console.log('❌ 请求错误:', error.message);
  }
}

/**
 * 测试实际的前端工具调用流程
 */
async function testActualFrontendFlow() {
  console.log('\n\n🌐 测试实际的前端工具调用流程...\n');
  
  // 模拟 Claude 返回的 tool_use 格式
  const mockClaudeResponse = {
    id: "msg_test",
    type: "message",
    role: "assistant",
    model: "claude-3-haiku-20240307",
    content: [
      {
        type: "tool_use",
        id: "toolu_test123",
        name: "write_file",
        input: {
          path: "/Users/<USER>/Desktop/frontend_flow_test.txt",
          content: "Frontend flow test content"
        }
      }
    ],
    stop_reason: "tool_use"
  };
  
  console.log('模拟 Claude 响应:');
  console.log(JSON.stringify(mockClaudeResponse, null, 2));
  
  console.log('\n处理响应:');
  const processed = simulateFrontendToolCallProcessing(mockClaudeResponse);
  
  if (processed) {
    console.log('\n✅ 前端流程测试成功');
  } else {
    console.log('\n❌ 前端流程测试失败');
  }
}

/**
 * 运行所有测试
 */
async function runAllTests() {
  console.log('🚀 开始测试 Claude tool_use 格式处理修复...\n');
  
  await testClaudeToolUseProcessing();
  await testActualFrontendFlow();
  
  console.log('\n🎯 测试总结:');
  console.log('1. 前端现在可以处理 Claude 原生的 tool_use 格式');
  console.log('2. 工具调用应该不再一直显示"正在等待结果..."');
  console.log('3. 工具应该被实际执行并显示结果');
  
  console.log('\n所有测试完成！');
}

runAllTests().catch(console.error);
