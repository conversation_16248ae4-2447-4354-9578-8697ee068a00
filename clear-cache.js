#!/usr/bin/env node

/**
 * 清除缓存脚本
 * 帮助前端获取最新的模型列表
 */

console.log('🧹 清除缓存指南');
console.log('================\n');

console.log('为了确保前端能够获取到最新的 Anthropic 模型列表，请执行以下步骤：\n');

console.log('1. 📱 在浏览器中：');
console.log('   - 按 F12 打开开发者工具');
console.log('   - 右键点击刷新按钮');
console.log('   - 选择 "清空缓存并硬性重新加载"\n');

console.log('2. 🔄 或者使用快捷键：');
console.log('   - Windows/Linux: Ctrl + Shift + R');
console.log('   - macOS: Cmd + Shift + R\n');

console.log('3. 🗂️ 清除本地存储：');
console.log('   - 在开发者工具中打开 Application/Storage 标签');
console.log('   - 清除 Local Storage 和 Session Storage\n');

console.log('4. ⚙️ 在设置面板中：');
console.log('   - 点击 "获取模型" 按钮');
console.log('   - 等待模型列表更新\n');

console.log('✅ 更新后您应该能看到以下新模型：');
console.log('   🚀 Claude Opus 4');
console.log('   🚀 Claude Sonnet 4');
console.log('   ⭐ Claude Sonnet 3.7');
console.log('   🔥 Claude Sonnet 3.5 (New)');
console.log('   🔥 Claude Haiku 3.5');
console.log('   📚 以及其他 Claude 3 系列模型\n');

console.log('🎉 模型列表已成功更新！');
