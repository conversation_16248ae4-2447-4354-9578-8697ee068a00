#!/usr/bin/env node

/**
 * 测试更新后的模型列表
 */

import fetch from 'node-fetch';

const API_KEY = '************************************************************************************************************';

/**
 * 测试获取模型列表
 */
async function testModelsList() {
  console.log('=== 测试 Anthropic 模型列表 ===\n');

  try {
    const response = await fetch('http://127.0.0.1:3001/api/anthropic/models', {
      method: 'GET',
      headers: {
        'x-api-key': API_KEY,
        'anthropic-version': '2023-06-01',
        'Content-Type': 'application/json'
      },
      timeout: 15000
    });

    console.log('响应状态:', response.status);

    if (response.ok) {
      const data = await response.json();
      console.log('✅ 模型列表获取成功');
      console.log('可用模型数量:', data.data?.length || 0);
      
      if (data.data && data.data.length > 0) {
        console.log('\n📋 完整模型列表:');
        data.data.forEach((model, index) => {
          console.log(`${index + 1}. ${model.id}`);
          console.log(`   显示名称: ${model.display_name}`);
          console.log(`   创建时间: ${model.created_at}`);
          console.log('');
        });
        
        // 按系列分组显示
        console.log('📊 按系列分组:');
        const claude4Models = data.data.filter(m => m.id.includes('claude-opus-4') || m.id.includes('claude-sonnet-4'));
        const claude37Models = data.data.filter(m => m.id.includes('claude-3-7'));
        const claude35Models = data.data.filter(m => m.id.includes('claude-3-5'));
        const claude3Models = data.data.filter(m => m.id.includes('claude-3-') && !m.id.includes('claude-3-5') && !m.id.includes('claude-3-7'));
        
        if (claude4Models.length > 0) {
          console.log('\n🚀 Claude 4 系列:');
          claude4Models.forEach(m => console.log(`  - ${m.display_name} (${m.id})`));
        }
        
        if (claude37Models.length > 0) {
          console.log('\n⭐ Claude 3.7 系列:');
          claude37Models.forEach(m => console.log(`  - ${m.display_name} (${m.id})`));
        }
        
        if (claude35Models.length > 0) {
          console.log('\n🔥 Claude 3.5 系列:');
          claude35Models.forEach(m => console.log(`  - ${m.display_name} (${m.id})`));
        }
        
        if (claude3Models.length > 0) {
          console.log('\n📚 Claude 3 系列:');
          claude3Models.forEach(m => console.log(`  - ${m.display_name} (${m.id})`));
        }
      }
      
      return true;
    } else {
      const errorText = await response.text();
      console.log('❌ 模型列表获取失败:', errorText);
      return false;
    }

  } catch (error) {
    console.log('❌ 模型列表请求错误:', error.message);
    return false;
  }
}

/**
 * 测试使用最新模型发送消息
 */
async function testLatestModel() {
  console.log('\n=== 测试最新模型 (Claude Opus 4) ===\n');

  const requestBody = {
    model: 'claude-opus-4-20250514',
    max_tokens: 500,
    messages: [
      {
        role: 'user',
        content: 'Hello! Can you tell me what model you are and what are your capabilities?'
      }
    ],
    temperature: 0.7
  };

  console.log('使用模型:', requestBody.model);

  try {
    const response = await fetch('http://127.0.0.1:3001/api/anthropic/messages', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': API_KEY,
        'anthropic-version': '2023-06-01'
      },
      body: JSON.stringify(requestBody)
    });

    console.log('响应状态:', response.status);

    if (response.ok) {
      const data = await response.json();
      console.log('✅ 请求成功');
      console.log('模型回复:', data.content[0].text);
      console.log('使用的模型:', data.model);
      console.log('Token 使用:', `输入: ${data.usage.input_tokens}, 输出: ${data.usage.output_tokens}`);
    } else {
      const errorText = await response.text();
      console.log('❌ 请求失败:', errorText);
    }

  } catch (error) {
    console.log('❌ 请求错误:', error.message);
  }
}

/**
 * 运行所有测试
 */
async function runAllTests() {
  console.log('开始测试更新后的模型列表...\n');

  // 测试模型列表
  const modelsOk = await testModelsList();
  
  if (modelsOk) {
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // 测试最新模型
    await testLatestModel();
  }

  console.log('\n所有测试完成！');
}

// 运行测试
runAllTests().catch(console.error);

export {
  testModelsList,
  testLatestModel,
  runAllTests
};
