import { ref, reactive } from 'vue';
import { MCPService, MCPToolCallParams } from '../services/MCPService';

export interface BatchToolCallItem {
  id: string;
  toolName: string;
  params: any;
  clientName?: string;
  enabled: boolean;
}

export interface BatchToolCallResult {
  id: string;
  index: number;
  toolName: string;
  params: any;
  result?: any;
  error?: string;
  success: boolean;
  duration: number;
  timestamp: string;
}

export interface BatchToolCallSession {
  id: string;
  name: string;
  items: BatchToolCallItem[];
  results?: BatchToolCallResult[];
  summary?: {
    total: number;
    successful: number;
    failed: number;
    totalDuration: number;
  };
  createdAt: string;
  executedAt?: string;
  status: 'draft' | 'running' | 'completed' | 'failed';
}

export function useBatchToolCall() {
  // 当前批量调用会话
  const currentSession = ref<BatchToolCallSession | null>(null);
  
  // 批量调用历史
  const sessions = ref<BatchToolCallSession[]>(
    JSON.parse(localStorage.getItem('batchToolCallSessions') || '[]')
  );
  
  // 执行状态
  const isExecuting = ref(false);
  const executionProgress = ref(0);
  const currentExecutingTool = ref<string>('');
  
  // 批量调用选项
  const batchOptions = reactive({
    parallel: false,
    maxConcurrency: 3,
    continueOnError: true,
    timeout: 30000
  });

  /**
   * 创建新的批量调用会话
   */
  function createNewSession(name?: string): string {
    const sessionId = Date.now().toString();
    const session: BatchToolCallSession = {
      id: sessionId,
      name: name || `批量调用 ${new Date().toLocaleString()}`,
      items: [],
      createdAt: new Date().toISOString(),
      status: 'draft'
    };
    
    currentSession.value = session;
    sessions.value.unshift(session);
    saveSessionsToStorage();
    
    return sessionId;
  }

  /**
   * 加载会话
   */
  function loadSession(sessionId: string): boolean {
    const session = sessions.value.find(s => s.id === sessionId);
    if (session) {
      currentSession.value = { ...session };
      return true;
    }
    return false;
  }

  /**
   * 添加工具调用项
   */
  function addToolCallItem(toolName: string, params: any, clientName?: string): string {
    if (!currentSession.value) {
      createNewSession();
    }
    
    const itemId = Date.now().toString() + Math.random().toString(36).substr(2, 9);
    const item: BatchToolCallItem = {
      id: itemId,
      toolName,
      params,
      clientName,
      enabled: true
    };
    
    currentSession.value!.items.push(item);
    saveCurrentSession();
    
    return itemId;
  }

  /**
   * 移除工具调用项
   */
  function removeToolCallItem(itemId: string): boolean {
    if (!currentSession.value) return false;
    
    const index = currentSession.value.items.findIndex(item => item.id === itemId);
    if (index !== -1) {
      currentSession.value.items.splice(index, 1);
      saveCurrentSession();
      return true;
    }
    return false;
  }

  /**
   * 更新工具调用项
   */
  function updateToolCallItem(itemId: string, updates: Partial<BatchToolCallItem>): boolean {
    if (!currentSession.value) return false;
    
    const item = currentSession.value.items.find(item => item.id === itemId);
    if (item) {
      Object.assign(item, updates);
      saveCurrentSession();
      return true;
    }
    return false;
  }

  /**
   * 切换工具调用项的启用状态
   */
  function toggleToolCallItem(itemId: string): boolean {
    if (!currentSession.value) return false;
    
    const item = currentSession.value.items.find(item => item.id === itemId);
    if (item) {
      item.enabled = !item.enabled;
      saveCurrentSession();
      return true;
    }
    return false;
  }

  /**
   * 执行批量工具调用
   */
  async function executeBatchToolCall(): Promise<boolean> {
    if (!currentSession.value || isExecuting.value) {
      return false;
    }

    const enabledItems = currentSession.value.items.filter(item => item.enabled);
    if (enabledItems.length === 0) {
      throw new Error('没有启用的工具调用项');
    }

    isExecuting.value = true;
    executionProgress.value = 0;
    currentSession.value.status = 'running';
    currentSession.value.executedAt = new Date().toISOString();

    try {
      // 准备工具调用参数
      const toolCalls: MCPToolCallParams[] = enabledItems.map(item => ({
        name: item.toolName,
        arguments: item.params,
        clientName: item.clientName
      }));

      console.log(`开始执行批量工具调用，共 ${toolCalls.length} 个工具`);

      // 执行批量调用
      const batchResult = await MCPService.callToolsBatch(toolCalls, {
        parallel: batchOptions.parallel,
        maxConcurrency: batchOptions.maxConcurrency,
        continueOnError: batchOptions.continueOnError,
        timeout: batchOptions.timeout
      });

      // 处理结果
      const results: BatchToolCallResult[] = batchResult.results.map((result, index) => {
        const originalItem = enabledItems[result.index];
        return {
          id: originalItem.id,
          index: result.index,
          toolName: result.toolName,
          params: result.params,
          result: result.result,
          error: result.error,
          success: result.success,
          duration: result.duration,
          timestamp: new Date().toISOString()
        };
      });

      // 更新会话
      currentSession.value.results = results;
      currentSession.value.summary = batchResult.summary;
      currentSession.value.status = 'completed';
      
      executionProgress.value = 100;
      saveCurrentSession();

      console.log('批量工具调用执行完成:', batchResult.summary);
      return true;

    } catch (error) {
      console.error('批量工具调用执行失败:', error);
      currentSession.value.status = 'failed';
      saveCurrentSession();
      throw error;
    } finally {
      isExecuting.value = false;
      currentExecutingTool.value = '';
    }
  }

  /**
   * 删除会话
   */
  function deleteSession(sessionId: string): boolean {
    const index = sessions.value.findIndex(s => s.id === sessionId);
    if (index !== -1) {
      sessions.value.splice(index, 1);
      saveSessionsToStorage();
      
      // 如果删除的是当前会话，清空当前会话
      if (currentSession.value?.id === sessionId) {
        currentSession.value = null;
      }
      
      return true;
    }
    return false;
  }

  /**
   * 复制会话
   */
  function duplicateSession(sessionId: string): string | null {
    const session = sessions.value.find(s => s.id === sessionId);
    if (!session) return null;
    
    const newSessionId = Date.now().toString();
    const newSession: BatchToolCallSession = {
      ...session,
      id: newSessionId,
      name: `${session.name} (副本)`,
      results: undefined,
      summary: undefined,
      createdAt: new Date().toISOString(),
      executedAt: undefined,
      status: 'draft',
      items: session.items.map(item => ({
        ...item,
        id: Date.now().toString() + Math.random().toString(36).substr(2, 9)
      }))
    };
    
    sessions.value.unshift(newSession);
    saveSessionsToStorage();
    
    return newSessionId;
  }

  /**
   * 导出会话为JSON
   */
  function exportSession(sessionId: string): string | null {
    const session = sessions.value.find(s => s.id === sessionId);
    if (!session) return null;
    
    return JSON.stringify(session, null, 2);
  }

  /**
   * 从JSON导入会话
   */
  function importSession(jsonData: string): string | null {
    try {
      const session: BatchToolCallSession = JSON.parse(jsonData);
      
      // 验证数据结构
      if (!session.id || !session.name || !Array.isArray(session.items)) {
        throw new Error('无效的会话数据格式');
      }
      
      // 生成新的ID避免冲突
      const newSessionId = Date.now().toString();
      session.id = newSessionId;
      session.name = `${session.name} (导入)`;
      session.createdAt = new Date().toISOString();
      session.status = 'draft';
      
      // 为所有项目生成新的ID
      session.items.forEach(item => {
        item.id = Date.now().toString() + Math.random().toString(36).substr(2, 9);
      });
      
      sessions.value.unshift(session);
      saveSessionsToStorage();
      
      return newSessionId;
    } catch (error) {
      console.error('导入会话失败:', error);
      return null;
    }
  }

  /**
   * 保存当前会话到存储
   */
  function saveCurrentSession(): void {
    if (!currentSession.value) return;
    
    const index = sessions.value.findIndex(s => s.id === currentSession.value!.id);
    if (index !== -1) {
      sessions.value[index] = { ...currentSession.value };
    }
    saveSessionsToStorage();
  }

  /**
   * 保存所有会话到本地存储
   */
  function saveSessionsToStorage(): void {
    localStorage.setItem('batchToolCallSessions', JSON.stringify(sessions.value));
  }

  /**
   * 清空所有会话
   */
  function clearAllSessions(): void {
    sessions.value = [];
    currentSession.value = null;
    saveSessionsToStorage();
  }

  /**
   * 获取会话统计信息
   */
  function getSessionStats() {
    const total = sessions.value.length;
    const completed = sessions.value.filter(s => s.status === 'completed').length;
    const failed = sessions.value.filter(s => s.status === 'failed').length;
    const draft = sessions.value.filter(s => s.status === 'draft').length;
    
    return {
      total,
      completed,
      failed,
      draft
    };
  }

  return {
    // 状态
    currentSession,
    sessions,
    isExecuting,
    executionProgress,
    currentExecutingTool,
    batchOptions,
    
    // 方法
    createNewSession,
    loadSession,
    addToolCallItem,
    removeToolCallItem,
    updateToolCallItem,
    toggleToolCallItem,
    executeBatchToolCall,
    deleteSession,
    duplicateSession,
    exportSession,
    importSession,
    clearAllSessions,
    getSessionStats
  };
}
