#!/usr/bin/env node

/**
 * 诊断工具调用状态问题
 */

import fetch from 'node-fetch';

const API_KEY = '************************************************************************************************************';

/**
 * 测试流式工具调用
 */
async function testStreamToolCall() {
  console.log('🌊 测试流式工具调用状态更新...\n');

  const requestBody = {
    model: 'claude-3-haiku-20240307',
    max_tokens: 1000,
    messages: [
      {
        role: 'user',
        content: '请帮我创建一个文件 /Users/<USER>/Desktop/status_test.txt，内容是 "Testing tool call status updates"'
      }
    ],
    tools: [
      {
        name: 'write_file',
        description: 'Write content to a file',
        input_schema: {
          type: 'object',
          properties: {
            path: {
              type: 'string',
              description: 'The file path to write to'
            },
            content: {
              type: 'string',
              description: 'The content to write to the file'
            }
          },
          required: ['path', 'content']
        }
      }
    ],
    stream: true,
    temperature: 0.7
  };

  console.log('发送流式请求...');
  
  try {
    const response = await fetch('http://127.0.0.1:3001/api/anthropic/messages', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': API_KEY,
        'anthropic-version': '2023-06-01'
      },
      body: JSON.stringify(requestBody)
    });

    console.log('响应状态:', response.status);

    if (response.ok && response.body) {
      console.log('✅ 开始接收流式响应...\n');
      
      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let buffer = '';
      let eventCount = 0;
      let toolCallFound = false;
      let toolCallCompleted = false;
      
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;
        
        buffer += decoder.decode(value, { stream: true });
        
        // 按行处理
        const lines = buffer.split('\n');
        buffer = lines.pop() || ''; // 保留最后一行（可能不完整）
        
        for (const line of lines) {
          eventCount++;
          
          if (line.startsWith('data: ')) {
            const data = line.slice(6);
            
            if (data === '[DONE]') {
              console.log(`[${eventCount}] 流式响应结束`);
              break;
            }
            
            try {
              const parsed = JSON.parse(data);
              
              // 检查工具调用相关的事件
              if (parsed.type === 'content_block_start' && parsed.content_block?.type === 'tool_use') {
                toolCallFound = true;
                console.log(`[${eventCount}] 🔧 工具调用开始:`, {
                  name: parsed.content_block.name,
                  id: parsed.content_block.id
                });
              }
              
              if (parsed.type === 'content_block_delta' && parsed.delta?.type === 'input_json_delta') {
                console.log(`[${eventCount}] 📝 工具参数增量:`, parsed.delta.partial_json);
              }
              
              if (parsed.type === 'content_block_stop') {
                if (toolCallFound && !toolCallCompleted) {
                  toolCallCompleted = true;
                  console.log(`[${eventCount}] ✅ 工具调用定义完成`);
                  
                  // 这里应该触发实际的工具执行
                  console.log('⚠️  注意：Claude 只是定义了工具调用，但没有执行！');
                  console.log('前端需要解析工具调用并执行，然后发送结果回给 Claude');
                }
              }
              
              if (parsed.type === 'message_stop') {
                console.log(`[${eventCount}] 🏁 消息结束`);
              }
              
            } catch (e) {
              // 忽略解析错误
            }
          }
        }
      }
      
      console.log('\n📊 流式响应分析:');
      console.log(`- 总事件数: ${eventCount}`);
      console.log(`- 发现工具调用: ${toolCallFound ? '是' : '否'}`);
      console.log(`- 工具调用完成: ${toolCallCompleted ? '是' : '否'}`);
      
      if (toolCallFound && toolCallCompleted) {
        console.log('\n💡 问题分析:');
        console.log('Claude 成功定义了工具调用，但前端需要：');
        console.log('1. 解析工具调用参数');
        console.log('2. 执行实际的工具调用');
        console.log('3. 将结果发送回给 Claude');
        console.log('4. 更新前端显示状态');
      }
      
    } else {
      const errorText = await response.text();
      console.log('❌ 流式响应失败:', errorText);
    }

  } catch (error) {
    console.log('❌ 请求错误:', error.message);
  }
}

/**
 * 测试前端流式处理端点
 */
async function testFrontendStreamEndpoint() {
  console.log('\n\n🎯 测试前端流式处理端点...\n');
  
  try {
    const response = await fetch('http://127.0.0.1:3001/api/chat/stream', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        message: '请帮我创建一个文件 /Users/<USER>/Desktop/frontend_test.txt，内容是 "Frontend stream test"',
        model: 'claude-3-haiku-20240307'
      })
    });
    
    if (response.ok && response.body) {
      console.log('✅ 前端流式端点响应正常');
      
      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let buffer = '';
      let chunkCount = 0;
      let toolCallStartFound = false;
      let toolCallResultFound = false;
      
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;
        
        const chunk = decoder.decode(value, { stream: true });
        buffer += chunk;
        chunkCount++;
        
        console.log(`[块 ${chunkCount}] ${chunk.substring(0, 100)}${chunk.length > 100 ? '...' : ''}`);
        
        // 检查工具调用标记
        if (chunk.includes('<tool-call') && chunk.includes('status="pending"')) {
          toolCallStartFound = true;
          console.log('🔧 发现工具调用开始标记');
        }
        
        if (chunk.includes('<tool-call') && (chunk.includes('status="success"') || chunk.includes('status="error"'))) {
          toolCallResultFound = true;
          console.log('✅ 发现工具调用结果标记');
        }
        
        // 检查工具调用JSON
        const toolCallMatches = chunk.match(/\{"type":"tool_calls"[^}]+\}/g);
        if (toolCallMatches) {
          toolCallMatches.forEach(match => {
            try {
              const toolCallData = JSON.parse(match);
              console.log('📋 发现工具调用JSON:', JSON.stringify(toolCallData, null, 2));
            } catch (e) {
              // 忽略解析错误
            }
          });
        }
      }
      
      console.log('\n📊 前端流式处理分析:');
      console.log(`- 总块数: ${chunkCount}`);
      console.log(`- 工具调用开始: ${toolCallStartFound ? '是' : '否'}`);
      console.log(`- 工具调用结果: ${toolCallResultFound ? '是' : '否'}`);
      
      if (!toolCallStartFound || !toolCallResultFound) {
        console.log('\n⚠️  问题发现:');
        if (!toolCallStartFound) {
          console.log('- 没有发现工具调用开始标记');
        }
        if (!toolCallResultFound) {
          console.log('- 没有发现工具调用结果标记');
          console.log('- 这可能导致前端一直显示"正在调用中..."');
        }
      }
      
    } else {
      console.log('❌ 前端流式端点失败');
    }
  } catch (error) {
    console.log('❌ 前端流式端点错误:', error.message);
  }
}

/**
 * 运行所有诊断
 */
async function runDiagnostics() {
  console.log('🔍 开始工具调用状态诊断...\n');
  
  await testStreamToolCall();
  await testFrontendStreamEndpoint();
  
  console.log('\n🎯 诊断建议:');
  console.log('1. 检查前端是否正确解析工具调用');
  console.log('2. 确认工具执行后状态更新机制');
  console.log('3. 验证工具调用超时处理');
  console.log('4. 检查前端组件状态绑定');
  
  console.log('\n诊断完成！');
}

runDiagnostics().catch(console.error);
