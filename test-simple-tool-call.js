#!/usr/bin/env node

/**
 * 简单的工具调用测试
 */

import fetch from 'node-fetch';

/**
 * 测试直接工具调用
 */
async function testSimpleToolCall() {
  console.log('🔧 测试简单工具调用...\n');

  const toolCallData = {
    name: 'write_file',
    arguments: {
      path: '/Users/<USER>/Desktop/simple_test.txt',
      content: 'This is a simple test file created by direct tool call.'
    }
  };

  console.log('调用工具:', toolCallData.name);
  console.log('参数:', JSON.stringify(toolCallData.arguments, null, 2));

  try {
    const response = await fetch('http://127.0.0.1:3001/mcp/tools/call', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(toolCallData)
    });

    console.log('响应状态:', response.status);

    if (response.ok) {
      const result = await response.json();
      console.log('✅ 工具调用成功');
      console.log('完整响应:', JSON.stringify(result, null, 2));
      
      // 分析结果结构
      if (result.result) {
        console.log('\n📋 结果分析:');
        console.log('- 结果类型:', typeof result.result);
        console.log('- 结果内容:', result.result);
        
        if (result.result.content) {
          console.log('- 内容数组:', result.result.content);
          if (Array.isArray(result.result.content)) {
            result.result.content.forEach((item, index) => {
              console.log(`  [${index}] 类型: ${item.type}, 内容: ${item.text}`);
            });
          }
        }
      }
      
      return result;
    } else {
      const errorText = await response.text();
      console.log('❌ 工具调用失败:', errorText);
      return null;
    }

  } catch (error) {
    console.log('❌ 请求错误:', error.message);
    return null;
  }
}

/**
 * 测试工具调用结果的不同格式
 */
async function testToolCallResultFormats() {
  console.log('\n🔍 测试不同的工具调用结果格式...\n');
  
  // 测试1: 写文件
  console.log('测试1: 写文件工具');
  const writeResult = await testSimpleToolCall();
  
  if (writeResult) {
    // 模拟前端处理这个结果
    console.log('\n🎨 模拟前端处理:');
    
    // 检查结果是否为 null
    if (writeResult.result === null) {
      console.log('⚠️  结果为 null - 这就是问题所在！');
    } else if (writeResult.result && writeResult.result.content) {
      console.log('✅ 结果有内容');
      
      // 模拟前端显示逻辑
      const displayResult = writeResult.result.content.map(item => item.text).join('\n');
      console.log('前端显示内容:', displayResult);
    } else {
      console.log('⚠️  结果格式异常:', writeResult.result);
    }
  }
  
  // 测试2: 列出目录
  console.log('\n测试2: 列出目录工具');
  await testListDirectory();
}

/**
 * 测试列出目录工具
 */
async function testListDirectory() {
  const toolCallData = {
    name: 'list_directory',
    arguments: {
      path: '/Users/<USER>/Desktop'
    }
  };

  try {
    const response = await fetch('http://127.0.0.1:3001/mcp/tools/call', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(toolCallData)
    });

    if (response.ok) {
      const result = await response.json();
      console.log('✅ 列出目录成功');
      console.log('目录内容结果:', JSON.stringify(result, null, 2));
    } else {
      const errorText = await response.text();
      console.log('❌ 列出目录失败:', errorText);
    }
  } catch (error) {
    console.log('❌ 列出目录错误:', error.message);
  }
}

// 运行测试
testToolCallResultFormats().catch(console.error);
